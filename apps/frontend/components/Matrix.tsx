/**
 * 矩阵主组件
 * 🎯 核心价值：纯渲染组件，数据驱动视图，零业务逻辑
 * 📦 功能范围：矩阵渲染、交互事件传递、性能优化
 * 🔄 架构设计：完全无状态组件，所有逻辑通过props注入
 */

'use client';

import React, { useEffect, useRef, useCallback, memo, useState } from 'react';
import { useMatrixStore, useMatrixData, useMatrixConfig } from '@/core/matrix/MatrixStore';
import { matrixCore } from '@/core/matrix/MatrixCore';
import { renderEngine } from '@/core/render/RenderEngine';
import type {
  MatrixConfig,
  InteractionEvent,
  Coordinate,
  BusinessMode,
} from '@/core/matrix/MatrixTypes';

import { createInteractionEvent } from '@/core/matrix/MatrixCore';

// ===== 组件属性 =====

interface MatrixProps {
  /** 自定义配置覆盖 */
  configOverride?: Partial<MatrixConfig>;
  
  /** 容器样式 */
  className?: string;
  style?: React.CSSProperties;
  
  /** 交互事件回调 */
  onCellClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellDoubleClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellHover?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellFocus?: (coordinate: Coordinate, event: React.FocusEvent) => void;
  onModeChange?: (mode: BusinessMode) => void;
  
  /** 性能配置 */
  enablePerformanceMonitoring?: boolean;
  debugMode?: boolean;
}

// ===== 主组件 =====

const MatrixComponent: React.FC<MatrixProps> = ({
  configOverride,
  className = '',
  style,
  onCellClick,
  onCellDoubleClick,
  onCellHover,
  onCellFocus,
  onModeChange,
  enablePerformanceMonitoring = false,
  debugMode = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const isInitialized = useRef(false);
  const [isClient, setIsClient] = useState(false);
  
  // 获取状态
  const matrixData = useMatrixData();
  const matrixConfig = useMatrixConfig();
  const {
    initializeMatrix,
    selectCell,
    hoverCell,
    focusCell,
    setMode,
    getProcessedData,
    getMatrixDataByCoordinate,
    hasMatrixData: hasMatrixDataAtCoordinate,
  } = useMatrixStore();
  
  // 合并配置
  const finalConfig = { ...matrixConfig, ...configOverride };

  // 确保客户端渲染一致性
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初始化
  useEffect(() => {
    if (!isInitialized.current && containerRef.current && isClient) {
      // 初始化矩阵数据
      if (matrixData.cells.size === 0) {
        initializeMatrix();
      }

      // 初始化渲染引擎
      renderEngine.initialize(containerRef.current, {
        width: containerRef.current.clientWidth,
        height: containerRef.current.clientHeight,
        scrollX: 0,
        scrollY: 0,
        zoom: 1,
      });

      isInitialized.current = true;
    }
  }, [initializeMatrix, matrixData.cells.size, isClient]);
  
  // 渲染矩阵
  useEffect(() => {
    if (isInitialized.current && matrixData.cells.size > 0) {
      const processedData = getProcessedData();
      renderEngine.render(processedData);

      if (enablePerformanceMonitoring) {
        const metrics = renderEngine.getPerformanceMetrics();
        console.log('Render metrics:', metrics);
      }
    }
  }, [matrixData, finalConfig, getProcessedData, enablePerformanceMonitoring]);
  
  // 处理单元格点击
  const handleCellClick = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);
    
    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      
      // 更新状态
      selectCell(x, y, event.ctrlKey || event.metaKey);
      
      // 创建交互事件
      const interactionEvent = createInteractionEvent('click', coordinate, {
        ctrl: event.ctrlKey,
        shift: event.shiftKey,
        alt: event.altKey,
      });
      
      // 处理业务逻辑
      const cell = matrixData.cells.get(`${x},${y}`);
      if (cell) {
        matrixCore.handleInteraction(interactionEvent, cell, finalConfig);
      }
      
      // 调用外部回调
      onCellClick?.(coordinate, event);
    }
  }, [matrixData.cells, finalConfig, selectCell, onCellClick]);
  
  // 处理双击
  const handleCellDoubleClick = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);
    
    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      onCellDoubleClick?.(coordinate, event);
    }
  }, [onCellDoubleClick]);
  
  // 处理悬停
  const handleCellMouseEnter = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);
    
    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      hoverCell(x, y);
      onCellHover?.(coordinate, event);
    }
  }, [hoverCell, onCellHover]);
  
  // 处理焦点
  const handleCellFocus = useCallback((event: React.FocusEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);
    
    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      focusCell(x, y);
      onCellFocus?.(coordinate, event);
    }
  }, [focusCell, onCellFocus]);
  
  // 处理键盘事件
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    // 模式切换快捷键
    switch (event.key) {
      case '1':
        if (event.ctrlKey) {
          setMode('coordinate');
          onModeChange?.('coordinate');
          event.preventDefault();
        }
        break;
      case '2':
        if (event.ctrlKey) {
          setMode('color');
          onModeChange?.('color');
          event.preventDefault();
        }
        break;
      case '3':
        if (event.ctrlKey) {
          setMode('value');
          onModeChange?.('value');
          event.preventDefault();
        }
        break;
      case '4':
        if (event.ctrlKey) {
          setMode('word');
          onModeChange?.('word');
          event.preventDefault();
        }
        break;
    }
  }, [setMode, onModeChange]);
  
  // 容器样式
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    userSelect: 'none',
    ...style,
  };
  
  // 调试信息
  const debugInfo = debugMode && isClient ? (
    <div className="absolute top-2 left-2 bg-black bg-opacity-75 text-white p-2 text-xs rounded z-10">
      <div>Mode: {finalConfig.mode}</div>
      <div>Cells: {matrixData.cells.size}</div>
      <div>Selected: {matrixData.selectedCells.size}</div>
      <div>Hovered: {matrixData.hoveredCell || 'none'}</div>
      {enablePerformanceMonitoring && (
        <div>FPS: {renderEngine.getPerformanceMetrics().frameRate.toFixed(1)}</div>
      )}
    </div>
  ) : null;

  // 在客户端渲染完成前显示占位符
  if (!isClient) {
    return (
      <div
        className={`matrix-container ${className}`}
        style={{ ...containerStyle, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      >
        <div className="text-gray-500">矩阵加载中...</div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`matrix-container ${className}`}
      style={containerStyle}
      onClick={handleCellClick}
      onDoubleClick={handleCellDoubleClick}
      onMouseEnter={handleCellMouseEnter}
      onFocus={handleCellFocus}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="grid"
      aria-label="矩阵网格"
      aria-rowcount={33}
      aria-colcount={33}
    >
      {debugInfo}
    </div>
  );
};

// ===== 性能优化 =====

const Matrix = memo(MatrixComponent, (prevProps, nextProps) => {
  // 自定义比较函数，避免不必要的重渲染
  return (
    prevProps.configOverride === nextProps.configOverride &&
    prevProps.className === nextProps.className &&
    prevProps.debugMode === nextProps.debugMode &&
    prevProps.enablePerformanceMonitoring === nextProps.enablePerformanceMonitoring
  );
});

Matrix.displayName = 'Matrix';

export default Matrix;
