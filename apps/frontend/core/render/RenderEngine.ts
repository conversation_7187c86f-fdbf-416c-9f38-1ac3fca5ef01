/**
 * 矩阵渲染引擎
 * 🎯 核心价值：高性能矩阵渲染，数据驱动视图更新，智能缓存优化
 * 📦 功能范围：渲染管道、视口管理、批量更新、性能监控
 * 🔄 架构设计：基于虚拟DOM概念的高效渲染，支持增量更新和智能缓存
 */

import type {
  MatrixData,
  MatrixConfig,
  ProcessedMatrixData,
  CellRenderData,
  ViewportConfig,
  RenderConfig,
  PerformanceMetrics,
  Coordinate,
} from '../matrix/MatrixTypes';

import {
  coordinateKey,
  DEFAULT_RENDER_CONFIG,
} from '../matrix/MatrixTypes';

// ===== 渲染状态 =====

interface RenderState {
  lastRenderTime: number;
  frameCount: number;
  renderQueue: Set<string>;
  dirtyRegions: Set<string>;
  isRendering: boolean;
}

interface CellRenderNode {
  key: string;
  coordinate: Coordinate;
  renderData: CellRenderData;
  element?: HTMLElement;
  isDirty: boolean;
  lastUpdate: number;
}

// ===== 渲染引擎 =====

export class RenderEngine {
  private config: RenderConfig;
  private viewport: ViewportConfig;
  private renderState: RenderState;
  private renderNodes: Map<string, CellRenderNode>;
  private renderCache: Map<string, CellRenderData>;
  private performanceMetrics: PerformanceMetrics;
  private animationFrameId: number | null = null;
  private container: HTMLElement | null = null;

  constructor(config: Partial<RenderConfig> = {}) {
    this.config = { ...DEFAULT_RENDER_CONFIG, ...config };
    this.viewport = {
      width: 0,
      height: 0,
      scrollX: 0,
      scrollY: 0,
      zoom: 1,
    };
    this.renderState = {
      lastRenderTime: 0,
      frameCount: 0,
      renderQueue: new Set(),
      dirtyRegions: new Set(),
      isRendering: false,
    };
    this.renderNodes = new Map();
    this.renderCache = new Map();
    this.performanceMetrics = {
      renderTime: 0,
      updateTime: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      frameRate: 60,
    };
  }
  
  /**
   * 初始化渲染引擎
   */
  initialize(container: HTMLElement, viewport: ViewportConfig): void {
    this.container = container;
    this.viewport = viewport;
    this.setupContainer(container);
  }
  
  /**
   * 渲染矩阵数据
   */
  render(processedData: ProcessedMatrixData): void {
    if (this.renderState.isRendering) {
      return; // 防止重复渲染
    }
    
    const startTime = performance.now();
    this.renderState.isRendering = true;
    
    try {
      this.updateRenderNodes(processedData);
      this.scheduleRender();
    } finally {
      this.renderState.isRendering = false;
      const endTime = performance.now();
      this.updatePerformanceMetrics(endTime - startTime);
    }
  }
  
  /**
   * 增量更新
   */
  updateCells(updates: Map<string, CellRenderData>): void {
    const startTime = performance.now();
    
    updates.forEach((renderData, key) => {
      const node = this.renderNodes.get(key);
      if (node) {
        // 检查是否真的需要更新
        if (this.shouldUpdateNode(node, renderData)) {
          node.renderData = renderData;
          node.isDirty = true;
          node.lastUpdate = Date.now();
          this.renderState.renderQueue.add(key);
        }
      } else {
        // 创建新节点
        const coordinate = this.parseCoordinateKey(key);
        this.renderNodes.set(key, {
          key,
          coordinate,
          renderData,
          isDirty: true,
          lastUpdate: Date.now(),
        });
        this.renderState.renderQueue.add(key);
      }
    });
    
    const endTime = performance.now();
    this.performanceMetrics.updateTime = endTime - startTime;
    
    this.scheduleRender();
  }
  
  /**
   * 批量渲染
   */
  private scheduleRender(): void {
    if (this.animationFrameId) {
      return; // 已经安排了渲染
    }
    
    this.animationFrameId = requestAnimationFrame(() => {
      this.performBatchRender();
      this.animationFrameId = null;
    });
  }
  
  /**
   * 执行批量渲染
   */
  private performBatchRender(): void {
    const startTime = performance.now();
    const batchSize = this.config.batchSize;
    const renderQueue = Array.from(this.renderState.renderQueue);
    
    // 分批处理渲染队列
    for (let i = 0; i < renderQueue.length; i += batchSize) {
      const batch = renderQueue.slice(i, i + batchSize);
      this.renderBatch(batch);
      
      // 检查是否需要让出控制权
      if (performance.now() - startTime > this.config.updateThrottle) {
        // 剩余的放到下一帧
        const remaining = renderQueue.slice(i + batchSize);
        this.renderState.renderQueue = new Set(remaining);
        this.scheduleRender();
        return;
      }
    }
    
    // 清空渲染队列
    this.renderState.renderQueue.clear();
    
    const endTime = performance.now();
    this.performanceMetrics.renderTime = endTime - startTime;
    this.renderState.frameCount++;
  }
  
  /**
   * 渲染批次
   */
  private renderBatch(batch: string[]): void {
    batch.forEach(key => {
      const node = this.renderNodes.get(key);
      if (node && node.isDirty) {
        this.renderNode(node);
        node.isDirty = false;
      }
    });
  }
  
  /**
   * 渲染单个节点
   */
  private renderNode(node: CellRenderNode): void {
    if (!node.element) {
      node.element = this.createElement(node);
      // 将新创建的元素添加到容器中
      if (this.container) {
        this.container.appendChild(node.element);
      }
    }

    this.updateElement(node.element, node.renderData);

    // 更新缓存
    this.renderCache.set(node.key, { ...node.renderData });
  }
  
  /**
   * 创建DOM元素
   */
  private createElement(node: CellRenderNode): HTMLElement {
    const element = document.createElement('div');
    element.className = 'matrix-cell';
    element.dataset.x = node.coordinate.x.toString();
    element.dataset.y = node.coordinate.y.toString();
    element.dataset.key = node.key;
    
    // 设置基础样式
    element.style.position = 'absolute';
    element.style.display = 'flex';
    element.style.alignItems = 'center';
    element.style.justifyContent = 'center';
    element.style.userSelect = 'none';
    element.style.cursor = 'pointer';
    
    return element;
  }
  
  /**
   * 更新DOM元素
   */
  private updateElement(element: HTMLElement, renderData: CellRenderData): void {
    // 更新内容
    if (element.textContent !== renderData.content) {
      element.textContent = renderData.content;
    }

    // 更新样式
    Object.assign(element.style, renderData.style);

    // 确保基础定位样式
    if (!element.style.left || !element.style.top) {
      const x = parseInt(element.dataset.x || '0');
      const y = parseInt(element.dataset.y || '0');
      const cellSize = 20; // 默认单元格大小
      const gap = 1; // 默认间隙

      element.style.left = `${x * (cellSize + gap)}px`;
      element.style.top = `${y * (cellSize + gap)}px`;
      element.style.width = `${cellSize}px`;
      element.style.height = `${cellSize}px`;
    }

    // 更新类名
    if (element.className !== renderData.className) {
      element.className = renderData.className;
    }

    // 更新交互状态
    if (renderData.isInteractive) {
      element.style.pointerEvents = 'auto';
    } else {
      element.style.pointerEvents = 'none';
    }
  }
  
  /**
   * 更新渲染节点
   */
  private updateRenderNodes(processedData: ProcessedMatrixData): void {
    // 清理不存在的节点
    const existingKeys = new Set(processedData.renderData.keys());
    for (const key of this.renderNodes.keys()) {
      if (!existingKeys.has(key)) {
        this.removeNode(key);
      }
    }
    
    // 更新或创建节点
    processedData.renderData.forEach((renderData, key) => {
      const existingNode = this.renderNodes.get(key);
      
      if (existingNode) {
        if (this.shouldUpdateNode(existingNode, renderData)) {
          existingNode.renderData = renderData;
          existingNode.isDirty = true;
          existingNode.lastUpdate = Date.now();
          this.renderState.renderQueue.add(key);
        }
      } else {
        const coordinate = this.parseCoordinateKey(key);
        this.renderNodes.set(key, {
          key,
          coordinate,
          renderData,
          isDirty: true,
          lastUpdate: Date.now(),
        });
        this.renderState.renderQueue.add(key);
      }
    });
  }
  
  /**
   * 检查是否需要更新节点
   */
  private shouldUpdateNode(node: CellRenderNode, newRenderData: CellRenderData): boolean {
    const cached = this.renderCache.get(node.key);
    if (!cached) return true;
    
    // 深度比较渲染数据
    return (
      cached.content !== newRenderData.content ||
      cached.className !== newRenderData.className ||
      cached.isInteractive !== newRenderData.isInteractive ||
      !this.deepEqual(cached.style, newRenderData.style)
    );
  }
  
  /**
   * 移除节点
   */
  private removeNode(key: string): void {
    const node = this.renderNodes.get(key);
    if (node) {
      if (node.element && node.element.parentNode) {
        node.element.parentNode.removeChild(node.element);
      }
      this.renderNodes.delete(key);
      this.renderCache.delete(key);
      this.renderState.renderQueue.delete(key);
    }
  }
  
  /**
   * 设置容器
   */
  private setupContainer(container: HTMLElement): void {
    container.style.position = 'relative';
    container.style.overflow = 'hidden';
    container.style.width = '100%';
    container.style.height = '100%';
  }
  
  /**
   * 解析坐标键
   */
  private parseCoordinateKey(key: string): Coordinate {
    const [x, y] = key.split(',').map(Number);
    return { x, y };
  }
  
  /**
   * 深度比较对象
   */
  private deepEqual(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) return true;
    if (!obj1 || !obj2) return false;
    
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    for (const key of keys1) {
      if (obj1[key] !== obj2[key]) return false;
    }
    
    return true;
  }
  
  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(renderTime: number): void {
    this.performanceMetrics.renderTime = renderTime;
    
    // 计算帧率
    const now = performance.now();
    if (this.renderState.lastRenderTime > 0) {
      const deltaTime = now - this.renderState.lastRenderTime;
      this.performanceMetrics.frameRate = 1000 / deltaTime;
    }
    this.renderState.lastRenderTime = now;
    
    // 计算缓存命中率
    const totalNodes = this.renderNodes.size;
    const dirtyNodes = this.renderState.renderQueue.size;
    this.performanceMetrics.cacheHitRate = totalNodes > 0 ? (totalNodes - dirtyNodes) / totalNodes : 0;
    
    // 估算内存使用
    this.performanceMetrics.memoryUsage = this.estimateMemoryUsage();
  }
  
  /**
   * 估算内存使用
   */
  private estimateMemoryUsage(): number {
    // 简单估算：每个节点约100字节
    return this.renderNodes.size * 100;
  }
  
  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }
  
  /**
   * 清理资源
   */
  dispose(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
    
    // 清理所有节点
    this.renderNodes.forEach((node) => {
      if (node.element && node.element.parentNode) {
        node.element.parentNode.removeChild(node.element);
      }
    });
    
    this.renderNodes.clear();
    this.renderCache.clear();
    this.renderState.renderQueue.clear();
  }
  
  /**
   * 更新配置
   */
  updateConfig(config: Partial<RenderConfig>): void {
    this.config = { ...this.config, ...config };
  }
  
  /**
   * 更新视口
   */
  updateViewport(viewport: Partial<ViewportConfig>): void {
    this.viewport = { ...this.viewport, ...viewport };
  }
}

// ===== 单例实例 =====

export const renderEngine = new RenderEngine();
